<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-avatar">
      <image src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view wx:if="{{userInfo.isVerified}}" class="verified-badge">
        <text>✓</text>
      </view>
    </view>

    <view class="user-info">
      <text class="user-name">{{userInfo.realName || userInfo.username}}</text>
      <text class="user-role">{{getRoleText(currentRole)}}</text>
      <view wx:if="{{userInfo.department}}" class="user-department">
        <text>{{userInfo.department}}</text>
      </view>
    </view>

    <view class="user-actions">
      <view class="action-btn" bindtap="onEditProfile">
        <text>编辑</text>
      </view>
    </view>
  </view>

  <!-- 角色切换器 -->
  <view wx:if="{{isMultiRole}}" class="role-section">
    <role-switcher
      mode="full"
      custom-class="profile-role-switcher"
      bind:rolechange="onRoleChange"
    ></role-switcher>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item" data-type="orders" bindtap="onStatTap">
        <text class="stat-number">{{stats.myOrders}}</text>
        <text class="stat-label">我的工单</text>
      </view>
      <view class="stat-item" data-type="equipment" bindtap="onStatTap">
        <text class="stat-number">{{stats.myEquipment}}</text>
        <text class="stat-label">管理设备</text>
      </view>
      <view class="stat-item" data-type="messages" bindtap="onStatTap">
        <text class="stat-number">{{stats.unreadMessages}}</text>
        <text class="stat-label">未读消息</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" data-action="verify" bindtap="onMenuTap">
        <view class="menu-icon">
          <text>🛡️</text>
        </view>
        <text class="menu-text">实名认证</text>
        <view class="menu-status">
          <text class="status-text {{userInfo.isVerified ? 'verified' : 'unverified'}}">
            {{userInfo.isVerified ? '已认证' : '未认证'}}
          </text>
          <text class="menu-arrow">></text>
        </view>
      </view>

      <view class="menu-item" data-action="settings" bindtap="onMenuTap">
        <view class="menu-icon">
          <text>⚙️</text>
        </view>
        <text class="menu-text">设置</text>
        <view class="menu-status">
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" data-action="help" bindtap="onMenuTap">
        <view class="menu-icon">
          <text>❓</text>
        </view>
        <text class="menu-text">帮助与反馈</text>
        <view class="menu-status">
          <text class="menu-arrow">></text>
        </view>
      </view>

      <view class="menu-item" data-action="about" bindtap="onMenuTap">
        <view class="menu-icon">
          <text>ℹ️</text>
        </view>
        <text class="menu-text">关于我们</text>
        <view class="menu-status">
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item logout" bindtap="onLogout">
        <view class="menu-icon">
          <text>🚪</text>
        </view>
        <text class="menu-text">退出登录</text>
        <view class="menu-status">
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">版本 1.0.0</text>
  </view>
</view>