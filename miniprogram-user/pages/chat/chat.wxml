<!--pages/chat/chat.wxml-->
<view class="chat-container">
  <view class="page-header">
    <text class="page-title">消息中心</text>
  </view>

  <!-- 聊天列表 -->
  <view class="chat-list">
    <view
      class="chat-item"
      wx:for="{{messageList}}"
      wx:key="id"
      data-order-id="{{item.order.id}}"
      bindtap="onChatTap"
    >
      <view class="chat-avatar">
        <image src="/images/default-avatar.png" mode="aspectFill"></image>
        <view wx:if="{{item.unreadCount > 0}}" class="unread-badge">{{item.unreadCount}}</view>
      </view>

      <view class="chat-content">
        <view class="chat-header">
          <text class="chat-title">{{item.order.title}}</text>
          <text class="chat-time">{{formatTime(item.lastMessage.created_at)}}</text>
        </view>

        <view class="chat-meta">
          <text class="order-number">{{item.order.order_number}}</text>
          <view class="order-status status-{{item.order.status}}">
            {{getStatusText(item.order.status)}}
          </view>
        </view>

        <view class="last-message">
          <text class="sender-name">{{item.lastMessage.sender.real_name}}:</text>
          <text class="message-preview">{{item.lastMessage.content}}</text>
        </view>
      </view>

      <view class="chat-arrow">
        <text>></text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && messageList.length === 0}}">
    <image src="/images/empty-chat.png" mode="aspectFit"></image>
    <text class="empty-text">暂无消息</text>
    <text class="empty-subtitle">工单沟通消息将在这里显示</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>